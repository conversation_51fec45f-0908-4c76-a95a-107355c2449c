{"metadata": {"timestamp": "2025-06-19T00:44:40.640Z", "baseUrl": "http://localhost:3000", "totalTests": 26, "totalPassed": 25, "totalFailed": 1, "passRate": 96, "averageResponseTime": 16200}, "testSuites": [{"suiteName": "Invoke Endpoint Tests", "tests": [{"testName": "Plain text - simple prompt", "endpoint": "/api/llm/invoke", "method": "POST", "requestBody": {"prompt": "Say hello in exactly 5 words", "temperature": 0.1}, "response": {"text": "Hello, how are you today?"}, "status": 200, "success": true, "executionTime": 1406, "validationResults": {"hasExpectedFields": true, "responseFormat": "object", "dataTypes": {"text": "string"}}}, {"testName": "Plain text - with model specification", "endpoint": "/api/llm/invoke", "method": "POST", "requestBody": {"prompt": "Explain quantum computing in one sentence", "model": "openai/gpt-4o-mini", "temperature": 0.7}, "response": {"text": "Quantum computing is a type of computation that harnesses the principles of quantum mechanics to process information using quantum bits (qubits), which can exist in multiple states simultaneously, enabling vastly superior processing power for certain complex problems compared to classical computers."}, "status": 200, "success": true, "executionTime": 1921, "validationResults": {"hasExpectedFields": true, "responseFormat": "object", "dataTypes": {"text": "string"}}}, {"testName": "Structured output - simple schema", "endpoint": "/api/llm/invoke", "method": "POST", "requestBody": {"prompt": "Create a user profile for <PERSON>, age 30, software engineer", "response_json_schema": {"type": "object", "properties": {"name": {"type": "string"}, "age": {"type": "number"}, "profession": {"type": "string"}}}, "temperature": 0.3}, "response": {"name": "<PERSON>", "age": 30, "profession": "software engineer"}, "status": 200, "success": true, "executionTime": 1461, "validationResults": {"hasExpectedFields": true, "responseFormat": "object", "dataTypes": {"name": "string", "age": "number", "profession": "string"}}}, {"testName": "Structured output - complex nested schema", "endpoint": "/api/llm/invoke", "method": "POST", "requestBody": {"prompt": "Create a todo list with 3 tasks for a React developer", "response_json_schema": {"type": "object", "properties": {"title": {"type": "string"}, "tasks": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "task": {"type": "string"}, "priority": {"type": "string"}, "completed": {"type": "boolean"}}}}, "totalTasks": {"type": "number"}}}, "temperature": 0.5}, "response": {"title": "React Developer Todo List", "tasks": [{"id": 1, "task": "Set up React project environment", "priority": "High", "completed": false}, {"id": 2, "task": "Design component structure and layout", "priority": "Medium", "completed": false}, {"id": 3, "task": "Implement state management and hooks", "priority": "High", "completed": false}], "totalTasks": 3}, "status": 200, "success": true, "executionTime": 1472, "validationResults": {"hasExpectedFields": true, "responseFormat": "object", "dataTypes": {"title": "string", "tasks": "object", "totalTasks": "number"}}}, {"testName": "Error case - missing prompt", "endpoint": "/api/llm/invoke", "method": "POST", "requestBody": {"temperature": 0.7}, "response": {"error": "Prompt is required"}, "status": 400, "success": true, "executionTime": 58, "validationResults": {"hasExpectedFields": true, "responseFormat": "object", "dataTypes": {"error": "string"}}}, {"testName": "Error case - invalid temperature", "endpoint": "/api/llm/invoke", "method": "POST", "requestBody": {"prompt": "Test prompt", "temperature": 2}, "response": null, "status": 0, "success": false, "executionTime": 301128, "error": "fetch failed", "validationResults": {"hasExpectedFields": false, "responseFormat": "error", "dataTypes": {}}}], "summary": {"total": 6, "passed": 5, "failed": 1, "averageTime": 51241}}, {"suiteName": "Chat Endpoint Tests", "tests": [{"testName": "Simple chat - single message", "endpoint": "/api/llm/chat", "method": "POST", "requestBody": {"messages": [{"role": "user", "content": "What is 2+2?"}], "stream": false, "temperature": 0.1}, "response": {"message": {"role": "assistant", "content": "2 + 2 equals 4."}, "usage": {"promptTokens": 14, "completionTokens": 8, "totalTokens": 22}}, "status": 200, "success": true, "executionTime": 1658, "validationResults": {"hasExpectedFields": true, "responseFormat": "object", "dataTypes": {"message": "object", "usage": "object"}}}, {"testName": "Chat with system message", "endpoint": "/api/llm/chat", "method": "POST", "requestBody": {"messages": [{"role": "system", "content": "You are a helpful math tutor. Answer in exactly one sentence."}, {"role": "user", "content": "Explain what multiplication is"}], "stream": false, "temperature": 0.3}, "response": {"message": {"role": "assistant", "content": "Multiplication is a mathematical operation that combines groups of equal size to find the total amount, essentially repeated addition of the same number."}, "usage": {"promptTokens": 28, "completionTokens": 26, "totalTokens": 54}}, "status": 200, "success": true, "executionTime": 968, "validationResults": {"hasExpectedFields": true, "responseFormat": "object", "dataTypes": {"message": "object", "usage": "object"}}}, {"testName": "Multi-turn conversation", "endpoint": "/api/llm/chat", "method": "POST", "requestBody": {"messages": [{"role": "system", "content": "You are a coding assistant."}, {"role": "user", "content": "How do I create a React component?"}, {"role": "assistant", "content": "You can create a React component using function syntax..."}, {"role": "user", "content": "Can you show me an example?"}], "stream": false, "temperature": 0.5}, "response": {"message": {"role": "assistant", "content": "Certainly! Here's a simple example of a React functional component:\n\n```jsx\nimport React from 'react';\n\nfunction HelloWorld() {\n  return (\n    <div>\n      <h1>Hello, World!</h1>\n    </div>\n  );\n}\n\nexport default HelloWorld;\n```\n\n**How to use it:**\n\n1. Save this code in a file, e.g., `HelloWorld.js`.\n2. Import and include it in your main app component:\n\n```jsx\nimport React from 'react';\nimport HelloWorld from './HelloWorld';\n\nfunction App() {\n  return (\n    <div>\n      <HelloWorld />\n    </div>\n  );\n}\n\nexport default App;\n```\n\n**Notes:**\n- React components are typically named with PascalCase.\n- You can also create class components, but functional components with hooks are more common now.\n- Make sure you have React and ReactDOM installed in your project.\n\nWould you like an example with props or state?"}, "usage": {"promptTokens": 50, "completionTokens": 196, "totalTokens": 246}}, "status": 200, "success": true, "executionTime": 1398, "validationResults": {"hasExpectedFields": true, "responseFormat": "object", "dataTypes": {"message": "object", "usage": "object"}}}, {"testName": "Chat with different model", "endpoint": "/api/llm/chat", "method": "POST", "requestBody": {"messages": [{"role": "user", "content": "Write a haiku about coding"}], "model": "openai/gpt-4o-mini", "stream": false, "temperature": 0.8}, "response": {"message": {"role": "assistant", "content": "Lines of logic flow,  \nFingers dance on keys of light—  \nWorlds born from the code."}, "usage": {"promptTokens": 13, "completionTokens": 22, "totalTokens": 35}}, "status": 200, "success": true, "executionTime": 2149, "validationResults": {"hasExpectedFields": true, "responseFormat": "object", "dataTypes": {"message": "object", "usage": "object"}}}, {"testName": "Error case - missing messages", "endpoint": "/api/llm/chat", "method": "POST", "requestBody": {"stream": false, "temperature": 0.7}, "response": {"error": "Messages array is required"}, "status": 400, "success": true, "executionTime": 53, "validationResults": {"hasExpectedFields": true, "responseFormat": "object", "dataTypes": {"error": "string"}}}, {"testName": "Error case - empty messages array", "endpoint": "/api/llm/chat", "method": "POST", "requestBody": {"messages": [], "stream": false}, "response": {"error": "Messages array cannot be empty"}, "status": 400, "success": true, "executionTime": 47, "validationResults": {"hasExpectedFields": true, "responseFormat": "object", "dataTypes": {"error": "string"}}}, {"testName": "Error case - invalid message format", "endpoint": "/api/llm/chat", "method": "POST", "requestBody": {"messages": [{"role": "invalid_role", "content": "Test message"}], "stream": false}, "response": {"error": "Invalid message role: invalid_role. Must be one of: system, user, assistant"}, "status": 400, "success": true, "executionTime": 130, "validationResults": {"hasExpectedFields": true, "responseFormat": "object", "dataTypes": {"error": "string"}}}], "summary": {"total": 7, "passed": 7, "failed": 0, "averageTime": 915}}, {"suiteName": "Image Endpoint Tests", "tests": [{"testName": "Single image - simple prompt", "endpoint": "/api/llm/image", "method": "POST", "requestBody": {"prompt": "A simple geometric pattern with blue and white colors", "size": "1024x1024", "quality": "standard"}, "response": {"url": "https://yxcrpoocuh9rtell.public.blob.vercel-storage.com/llm-images/1750293808545-dalle3-lfx7ntySnXAkH27j3r6t7rj3Pz1DF7.png", "filename": "llm-images/1750293808545-dalle3.png", "prompt": "A simple geometric pattern with blue and white colors", "model": "dall-e-3", "size": "1024x1024", "quality": "standard", "downloadUrl": "https://yxcrpoocuh9rtell.public.blob.vercel-storage.com/llm-images/1750293808545-dalle3-lfx7ntySnXAkH27j3r6t7rj3Pz1DF7.png?download=1"}, "status": 200, "success": true, "executionTime": 17994, "validationResults": {"hasExpectedFields": true, "responseFormat": "object", "dataTypes": {"url": "string", "filename": "string", "prompt": "string", "model": "string", "size": "string", "quality": "string", "downloadUrl": "string"}}}, {"testName": "Single image - DALL-E 3 with HD quality", "endpoint": "/api/llm/image", "method": "POST", "requestBody": {"prompt": "A minimalist icon representing React components", "model": "dall-e-3", "size": "1024x1024", "quality": "hd"}, "response": {"url": "https://yxcrpoocuh9rtell.public.blob.vercel-storage.com/llm-images/1750293831501-dalle3-H9ScsyE5a46aq0lw9iZBUMXoEuc63b.png", "filename": "llm-images/1750293831501-dalle3.png", "prompt": "A minimalist icon representing React components", "model": "dall-e-3", "size": "1024x1024", "quality": "hd", "downloadUrl": "https://yxcrpoocuh9rtell.public.blob.vercel-storage.com/llm-images/1750293831501-dalle3-H9ScsyE5a46aq0lw9iZBUMXoEuc63b.png?download=1"}, "status": 200, "success": true, "executionTime": 23039, "validationResults": {"hasExpectedFields": true, "responseFormat": "object", "dataTypes": {"url": "string", "filename": "string", "prompt": "string", "model": "string", "size": "string", "quality": "string", "downloadUrl": "string"}}}, {"testName": "Single image - different size", "endpoint": "/api/llm/image", "method": "POST", "requestBody": {"prompt": "A landscape view of mountains at sunset", "model": "dall-e-3", "size": "1792x1024", "quality": "standard"}, "response": {"url": "https://yxcrpoocuh9rtell.public.blob.vercel-storage.com/llm-images/1750293855065-dalle3-okM6sx57RrWGEdnP4nZdr69uVFBIZP.png", "filename": "llm-images/1750293855065-dalle3.png", "prompt": "A landscape view of mountains at sunset", "model": "dall-e-3", "size": "1792x1024", "quality": "standard", "downloadUrl": "https://yxcrpoocuh9rtell.public.blob.vercel-storage.com/llm-images/1750293855065-dalle3-okM6sx57RrWGEdnP4nZdr69uVFBIZP.png?download=1"}, "status": 200, "success": true, "executionTime": 24543, "validationResults": {"hasExpectedFields": true, "responseFormat": "object", "dataTypes": {"url": "string", "filename": "string", "prompt": "string", "model": "string", "size": "string", "quality": "string", "downloadUrl": "string"}}}, {"testName": "Multiple images - DALL-E 2", "endpoint": "/api/llm/image", "method": "POST", "requestBody": {"prompt": "Simple geometric pattern", "model": "dall-e-2", "size": "1024x1024", "n": 2}, "response": {"images": [{"url": "https://yxcrpoocuh9rtell.public.blob.vercel-storage.com/llm-images/1750293876241-dalle2-1-jmQCH6b8km2xhgN3Jr5tuVFlJSOm10.png", "filename": "llm-images/1750293876241-dalle2-1.png", "downloadUrl": "https://yxcrpoocuh9rtell.public.blob.vercel-storage.com/llm-images/1750293876241-dalle2-1-jmQCH6b8km2xhgN3Jr5tuVFlJSOm10.png?download=1"}, {"url": "https://yxcrpoocuh9rtell.public.blob.vercel-storage.com/llm-images/1750293876242-dalle2-2-6Ykickm3Jg74uotMslHCpmkhjHISJa.png", "filename": "llm-images/1750293876242-dalle2-2.png", "downloadUrl": "https://yxcrpoocuh9rtell.public.blob.vercel-storage.com/llm-images/1750293876242-dalle2-2-6Ykickm3Jg74uotMslHCpmkhjHISJa.png?download=1"}], "prompt": "Simple geometric pattern", "model": "dall-e-2", "size": "1024x1024", "count": 2}, "status": 200, "success": true, "executionTime": 20310, "validationResults": {"hasExpectedFields": true, "responseFormat": "object", "dataTypes": {"images": "object", "prompt": "string", "model": "string", "size": "string", "count": "number"}}}, {"testName": "Error case - missing prompt", "endpoint": "/api/llm/image", "method": "POST", "requestBody": {"size": "1024x1024", "quality": "standard"}, "response": {"error": "Prompt is required"}, "status": 400, "success": true, "executionTime": 63, "validationResults": {"hasExpectedFields": true, "responseFormat": "object", "dataTypes": {"error": "string"}}}, {"testName": "Error case - invalid size", "endpoint": "/api/llm/image", "method": "POST", "requestBody": {"prompt": "Test image", "size": "500x500", "quality": "standard"}, "response": {"error": "Invalid size: 500x500. Must be one of: 1024x1024, 1792x1024, 1024x1792"}, "status": 400, "success": true, "executionTime": 47, "validationResults": {"hasExpectedFields": true, "responseFormat": "object", "dataTypes": {"error": "string"}}}, {"testName": "Error case - invalid model", "endpoint": "/api/llm/image", "method": "POST", "requestBody": {"prompt": "Test image", "model": "invalid-model", "size": "1024x1024"}, "response": {"error": "Model invalid-model is not allowed. Please contact Magically support to get it whitelisted."}, "status": 400, "success": true, "executionTime": 46, "validationResults": {"hasExpectedFields": true, "responseFormat": "object", "dataTypes": {"error": "string"}}}], "summary": {"total": 7, "passed": 7, "failed": 0, "averageTime": 12292}}, {"suiteName": "Edge Cases and Error Tests", "tests": [{"testName": "Get available models", "endpoint": "/api/llm/models", "method": "GET", "requestBody": {}, "response": {"text": ["openai/gpt-4.1-mini", "openai/gpt-4.1-nano", "openai/gpt-4.1", "openai/gpt-4o-mini", "openai/gpt-o3-mini-high", "google/gemini-2.5-flash", "anthropic/claude-3.5-haiku"], "image": ["dall-e-2", "dall-e-3"], "default": {"text": "openai/gpt-4.1-nano", "image": "dall-e-3"}}, "status": 200, "success": true, "executionTime": 46, "validationResults": {"hasExpectedFields": true, "responseFormat": "object", "dataTypes": {"text": "object", "image": "object", "default": "object"}}}, {"testName": "Invalid endpoint", "endpoint": "/api/llm/invalid", "method": "POST", "requestBody": {"prompt": "test"}, "response": {"error": "Endpoint not found"}, "status": 404, "success": true, "executionTime": 46, "validationResults": {"hasExpectedFields": true, "responseFormat": "object", "dataTypes": {"error": "string"}}}, {"testName": "OPTIONS request to invoke", "endpoint": "/api/llm/invoke", "method": "OPTIONS", "requestBody": {}, "response": {"status": 204, "statusText": "No Content"}, "status": 204, "success": true, "executionTime": 42, "validationResults": {"hasExpectedFields": false, "responseFormat": "object", "dataTypes": {"status": "number", "statusText": "string"}}}, {"testName": "GET request to chat (should fail)", "endpoint": "/api/llm/chat", "method": "GET", "requestBody": {}, "response": {"error": "Endpoint not found"}, "status": 404, "success": true, "executionTime": 44, "validationResults": {"hasExpectedFields": true, "responseFormat": "object", "dataTypes": {"error": "string"}}}, {"testName": "Large prompt test", "endpoint": "/api/llm/invoke", "method": "POST", "requestBody": {"prompt": "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", "temperature": 0.1}, "response": {"text": "Hello! It looks like you've sent a very long string of the letter \"A\". How can I assist you today?"}, "status": 200, "success": true, "executionTime": 1891, "validationResults": {"hasExpectedFields": true, "responseFormat": "object", "dataTypes": {"text": "string"}}}, {"testName": "Empty JSON body", "endpoint": "/api/llm/invoke", "method": "POST", "requestBody": {}, "response": {"error": "Prompt is required"}, "status": 400, "success": true, "executionTime": 48, "validationResults": {"hasExpectedFields": true, "responseFormat": "object", "dataTypes": {"error": "string"}}}], "summary": {"total": 6, "passed": 6, "failed": 0, "averageTime": 353}}], "failedTests": [{"suite": "Invoke Endpoint Tests", "test": "Error case - invalid temperature", "error": "fetch failed", "endpoint": "/api/llm/invoke", "status": 0}]}