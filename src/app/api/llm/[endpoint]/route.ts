import { NextRequest, NextResponse } from 'next/server';
import { generateText, generateObject, streamText, experimental_generateImage as generateImage } from 'ai';
import { customModel } from '@/lib/ai';
import { openai } from '@ai-sdk/openai';
import { z } from 'zod';

// Helper function to convert JSON schema to Zod schema
function jsonSchemaToZod(jsonSchema: any): z.ZodType<any> {
    if (!jsonSchema || !jsonSchema.properties) {
        return z.object({});
    }

    const zodProperties: Record<string, z.ZodType<any>> = {};

    for (const [key, value] of Object.entries(jsonSchema.properties)) {
        const prop = value as any;

        switch (prop.type) {
            case 'string':
                zodProperties[key] = z.string();
                break;
            case 'number':
                zodProperties[key] = z.number();
                break;
            case 'boolean':
                zodProperties[key] = z.boolean();
                break;
            case 'array':
                if (prop.items?.type === 'string') {
                    zodProperties[key] = z.array(z.string());
                } else if (prop.items?.type === 'number') {
                    zodProperties[key] = z.array(z.number());
                } else if (prop.items?.type === 'object') {
                    zodProperties[key] = z.array(jsonSchemaToZod(prop.items));
                } else {
                    zodProperties[key] = z.array(z.any());
                }
                break;
            case 'object':
                zodProperties[key] = jsonSchemaToZod(prop);
                break;
            default:
                zodProperties[key] = z.any();
        }
    }

    return z.object(zodProperties);
}

// CORS headers
const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'POST, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, x-app-id, x-origin-url, x-socket-id',
    'Access-Control-Max-Age': '86400',
};

// Handle OPTIONS request (preflight)
export async function OPTIONS(request: NextRequest, { params }: {
    params: Promise<{ endpoint: string }>
}) {
    return new NextResponse(null, {
        status: 204,
        headers: corsHeaders,
    });
}

export async function POST(request: NextRequest, { params }: {
    params: Promise<{ endpoint: string }>
}) {
    const { endpoint } = await params;

    try {
        const body = await request.json();

        // Handle /api/llm/invoke endpoint - Pure LLM calls with optional schema
        if (endpoint === 'invoke') {
            const { prompt, response_json_schema, model = 'gpt-4.1-nano', temperature = 0.7 } = body;

            if (!prompt) {
                return NextResponse.json(
                    { error: 'Prompt is required' },
                    { status: 400, headers: corsHeaders }
                );
            }

            try {
                if (response_json_schema) {
                    // Convert JSON schema to Zod schema and use generateObject for structured output
                    const zodSchema = jsonSchemaToZod(response_json_schema);
                    const result = await generateObject({
                        model: customModel(model || 'gpt-4.1-nano'),
                        prompt,
                        schema: zodSchema,
                        temperature,
                    });

                    return NextResponse.json(result.object, {
                        headers: corsHeaders,
                    });
                } else {
                    // Use generateText for plain text output
                    const result = await generateText({
                        model: customModel(model || 'gpt-4.1-nano'),
                        prompt,
                        temperature,
                    });

                    return NextResponse.json(
                        { text: result.text },
                        { headers: corsHeaders }
                    );
                }
            } catch (error) {
                console.error('Error in LLM invoke:', error);
                return NextResponse.json(
                    { error: 'Failed to generate response' },
                    { status: 500, headers: corsHeaders }
                );
            }
        }

        // Handle /api/llm/chat endpoint - Conversation with messages
        if (endpoint === 'chat') {
            const { messages, model = 'gpt-4.1-nano', temperature = 0.7, stream = false } = body;

            if (!messages || !Array.isArray(messages)) {
                return NextResponse.json(
                    { error: 'Messages array is required' },
                    { status: 400, headers: corsHeaders }
                );
            }

            if (messages.length === 0) {
                return NextResponse.json(
                    { error: 'Messages array cannot be empty' },
                    { status: 400, headers: corsHeaders }
                );
            }

            // Validate message format
            for (const message of messages) {
                if (!message.role || !message.content) {
                    return NextResponse.json(
                        { error: 'Each message must have role and content fields' },
                        { status: 400, headers: corsHeaders }
                    );
                }

                // Validate role values
                const validRoles = ['system', 'user', 'assistant'];
                if (!validRoles.includes(message.role)) {
                    return NextResponse.json(
                        { error: `Invalid message role: ${message.role}. Must be one of: ${validRoles.join(', ')}` },
                        { status: 400, headers: corsHeaders }
                    );
                }
            }

            try {
                if (stream) {
                    // Return streaming response
                    const result = streamText({
                        model: customModel(model || 'gpt-4.1-nano'),
                        messages,
                        temperature,
                    });

                    return result.toDataStreamResponse({
                        headers: corsHeaders,
                    });
                } else {
                    // Return complete response
                    const result = await generateText({
                        model: customModel(model || 'gpt-4.1-nano'),
                        messages,
                        temperature,
                    });

                    return NextResponse.json(
                        { 
                            message: {
                                role: 'assistant',
                                content: result.text
                            },
                            usage: result.usage
                        },
                        { headers: corsHeaders }
                    );
                }
            } catch (error) {
                console.error('Error in LLM chat:', error);
                return NextResponse.json(
                    { error: 'Failed to generate chat response' },
                    { status: 500, headers: corsHeaders }
                );
            }
        }

        // Handle /api/llm/image endpoint - Image generation
        if (endpoint === 'image') {
            const { prompt, model = 'dall-e-3', size = '1024x1024', quality = 'standard', n = 1 } = body;

            if (!prompt) {
                return NextResponse.json(
                    { error: 'Prompt is required' },
                    { status: 400, headers: corsHeaders }
                );
            }

            // Validate model
            const validModels = ['dall-e-2', 'dall-e-3'];
            if (!validModels.includes(model)) {
                return NextResponse.json(
                    { error: `Invalid model: ${model}. Must be one of: ${validModels.join(', ')}` },
                    { status: 400, headers: corsHeaders }
                );
            }

            // Validate size
            const validSizes = ['1024x1024', '1792x1024', '1024x1792'];
            if (!validSizes.includes(size)) {
                return NextResponse.json(
                    { error: `Invalid size: ${size}. Must be one of: ${validSizes.join(', ')}` },
                    { status: 400, headers: corsHeaders }
                );
            }

            // Validate n parameter
            if (n < 1 || n > 10) {
                return NextResponse.json(
                    { error: 'Number of images (n) must be between 1 and 10' },
                    { status: 400, headers: corsHeaders }
                );
            }

            // DALL-E 3 only supports n=1
            if (model === 'dall-e-3' && n > 1) {
                return NextResponse.json(
                    { error: 'DALL-E 3 only supports generating 1 image at a time (n=1)' },
                    { status: 400, headers: corsHeaders }
                );
            }

            try {
                // Prepare provider options - quality is only supported by DALL-E 3
                const providerOptions: any = {
                    openai: {}
                };

                if (model === 'dall-e-3') {
                    providerOptions.openai.quality = quality as 'standard' | 'hd';
                }

                // Use Vercel AI SDK's generateImage with OpenAI
                const result = await generateImage({
                    model: openai.image(model),
                    prompt,
                    size: size as '1024x1024' | '1792x1024' | '1024x1792',
                    n,
                    providerOptions,
                });

                // Handle single or multiple images
                if (n === 1) {
                    return NextResponse.json(
                        {
                            url: `data:image/png;base64,${result.image.base64}`,
                            base64: result.image.base64,
                            prompt,
                            model,
                            size,
                            quality
                        },
                        { headers: corsHeaders }
                    );
                } else {
                    return NextResponse.json(
                        {
                            images: result.images.map(img => ({
                                url: `data:image/png;base64,${img.base64}`,
                                base64: img.base64
                            })),
                            prompt,
                            model,
                            size,
                            quality,
                            count: result.images.length
                        },
                        { headers: corsHeaders }
                    );
                }
            } catch (error) {
                console.error('Error in image generation:', error);

                // Provide more specific error messages
                const errorMessage = error instanceof Error ? error.message : 'Unknown error';
                if (errorMessage.includes('rate limit') || errorMessage.includes('quota')) {
                    return NextResponse.json(
                        { error: 'Rate limit exceeded. Please try again later.' },
                        { status: 429, headers: corsHeaders }
                    );
                } else if (errorMessage.includes('content policy') || errorMessage.includes('safety')) {
                    return NextResponse.json(
                        { error: 'Image prompt violates content policy. Please modify your prompt.' },
                        { status: 400, headers: corsHeaders }
                    );
                } else {
                    return NextResponse.json(
                        { error: `Failed to generate image: ${errorMessage}` },
                        { status: 500, headers: corsHeaders }
                    );
                }
            }
        }

        return NextResponse.json(
            { error: 'Endpoint not found' },
            { status: 404, headers: corsHeaders }
        );

    } catch (error) {
        console.error('Error parsing request body:', error);
        return NextResponse.json(
            { error: 'Invalid request body' },
            { status: 400, headers: corsHeaders }
        );
    }
}
