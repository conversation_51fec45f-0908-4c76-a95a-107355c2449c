import { NextRequest, NextResponse } from 'next/server';
import { generateText, generateObject, streamText } from 'ai';
import { customModel } from '@/lib/ai';
import { openai } from '@ai-sdk/openai';
import { z } from 'zod';
import OpenAI from 'openai';

// Initialize OpenAI client for image generation
const openaiClient = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY!,
});

// Helper function to convert JSON schema to Zod schema
function jsonSchemaToZod(jsonSchema: any): z.ZodType<any> {
    if (!jsonSchema || !jsonSchema.properties) {
        return z.object({});
    }

    const zodProperties: Record<string, z.ZodType<any>> = {};

    for (const [key, value] of Object.entries(jsonSchema.properties)) {
        const prop = value as any;

        switch (prop.type) {
            case 'string':
                zodProperties[key] = z.string();
                break;
            case 'number':
                zodProperties[key] = z.number();
                break;
            case 'boolean':
                zodProperties[key] = z.boolean();
                break;
            case 'array':
                if (prop.items?.type === 'string') {
                    zodProperties[key] = z.array(z.string());
                } else if (prop.items?.type === 'number') {
                    zodProperties[key] = z.array(z.number());
                } else if (prop.items?.type === 'object') {
                    zodProperties[key] = z.array(jsonSchemaToZod(prop.items));
                } else {
                    zodProperties[key] = z.array(z.any());
                }
                break;
            case 'object':
                zodProperties[key] = jsonSchemaToZod(prop);
                break;
            default:
                zodProperties[key] = z.any();
        }
    }

    return z.object(zodProperties);
}

// CORS headers
const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'POST, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, x-app-id, x-origin-url, x-socket-id',
    'Access-Control-Max-Age': '86400',
};

// Handle OPTIONS request (preflight)
export async function OPTIONS(request: NextRequest, { params }: {
    params: Promise<{ endpoint: string }>
}) {
    return new NextResponse(null, {
        status: 204,
        headers: corsHeaders,
    });
}

export async function POST(request: NextRequest, { params }: {
    params: Promise<{ endpoint: string }>
}) {
    const { endpoint } = await params;

    try {
        const body = await request.json();

        // Handle /api/llm/invoke endpoint - Pure LLM calls with optional schema
        if (endpoint === 'invoke') {
            const { prompt, response_json_schema, model = 'gpt-4o-mini', temperature = 0.7 } = body;

            if (!prompt) {
                return NextResponse.json(
                    { error: 'Prompt is required' },
                    { status: 400, headers: corsHeaders }
                );
            }

            try {
                if (response_json_schema) {
                    // Convert JSON schema to Zod schema and use generateObject for structured output
                    const zodSchema = jsonSchemaToZod(response_json_schema);
                    const result = await generateObject({
                        model: customModel,
                        prompt,
                        schema: zodSchema,
                        temperature,
                    });

                    return NextResponse.json(result.object, {
                        headers: corsHeaders,
                    });
                } else {
                    // Use generateText for plain text output
                    const result = await generateText({
                        model: customModel,
                        prompt,
                        temperature,
                    });

                    return NextResponse.json(
                        { text: result.text },
                        { headers: corsHeaders }
                    );
                }
            } catch (error) {
                console.error('Error in LLM invoke:', error);
                return NextResponse.json(
                    { error: 'Failed to generate response' },
                    { status: 500, headers: corsHeaders }
                );
            }
        }

        // Handle /api/llm/chat endpoint - Conversation with messages
        if (endpoint === 'chat') {
            const { messages, model = 'gpt-4o-mini', temperature = 0.7, stream = false } = body;

            if (!messages || !Array.isArray(messages)) {
                return NextResponse.json(
                    { error: 'Messages array is required' },
                    { status: 400, headers: corsHeaders }
                );
            }

            try {
                if (stream) {
                    // Return streaming response
                    const result = streamText({
                        model: customModel,
                        messages,
                        temperature,
                    });

                    return result.toDataStreamResponse({
                        headers: corsHeaders,
                    });
                } else {
                    // Return complete response
                    const result = await generateText({
                        model: customModel,
                        messages,
                        temperature,
                    });

                    return NextResponse.json(
                        { 
                            message: {
                                role: 'assistant',
                                content: result.text
                            },
                            usage: result.usage
                        },
                        { headers: corsHeaders }
                    );
                }
            } catch (error) {
                console.error('Error in LLM chat:', error);
                return NextResponse.json(
                    { error: 'Failed to generate chat response' },
                    { status: 500, headers: corsHeaders }
                );
            }
        }

        // Handle /api/llm/image endpoint - Image generation
        if (endpoint === 'image') {
            const { prompt, model = 'dall-e-3', size = '1024x1024', quality = 'standard' } = body;

            if (!prompt) {
                return NextResponse.json(
                    { error: 'Prompt is required' },
                    { status: 400, headers: corsHeaders }
                );
            }

            try {
                // Use OpenAI's image generation
                const response = await openaiClient.images.generate({
                    model,
                    prompt,
                    size: size as '1024x1024' | '1792x1024' | '1024x1792',
                    quality: quality as 'standard' | 'hd',
                    n: 1,
                });

                const imageUrl = response.data[0]?.url;
                if (!imageUrl) {
                    throw new Error('No image URL returned from OpenAI');
                }

                return NextResponse.json(
                    {
                        url: imageUrl,
                        prompt,
                        model,
                        size,
                        quality
                    },
                    { headers: corsHeaders }
                );
            } catch (error) {
                console.error('Error in image generation:', error);
                return NextResponse.json(
                    { error: 'Failed to generate image' },
                    { status: 500, headers: corsHeaders }
                );
            }
        }

        return NextResponse.json(
            { error: 'Endpoint not found' },
            { status: 404, headers: corsHeaders }
        );

    } catch (error) {
        console.error('Error parsing request body:', error);
        return NextResponse.json(
            { error: 'Invalid request body' },
            { status: 400, headers: corsHeaders }
        );
    }
}
