/**
 * MongoDB Atlas Admin API Client
 * For programmatically creating database users with scoped permissions
 */

import crypto from 'crypto';
import DigestFetch from 'digest-fetch';

export interface MongoDBUser {
  username: string;
  password: string;
  roles: Array<{
    role: string;
    db: string;
  }>;
}

export interface AtlasUser {
  username: string;
  password: string;
  databaseName: string; // Required by Atlas API - usually 'admin'
  roles: Array<{
    roleName: string;
    databaseName: string;
  }>;
  scopes: Array<{
    name: string;
    type: string;
  }>;
}

export class MongoDBAtlasAdminClient {
  private publicKey: string;
  private privateKey: string;
  private groupId: string;
  private clusterName: string;
  private baseUrl = 'https://cloud.mongodb.com/api/atlas/v2';
  private digestFetch: DigestFetch;

  constructor() {
    this.publicKey = process.env.MONGODB_ATLAS_PUBLIC_KEY!;
    this.privateKey = process.env.MONGODB_ATLAS_PRIVATE_KEY!;
    this.groupId = process.env.MONGODB_ATLAS_GROUP_ID!;
    this.clusterName = process.env.MONGODB_ATLAS_CLUSTER_NAME!;

    if (!this.publicKey || !this.privateKey || !this.groupId || !this.clusterName) {
      throw new Error('MongoDB Atlas credentials not configured');
    }

    // Create digest fetch client
    this.digestFetch = new DigestFetch(this.publicKey, this.privateKey);
  }

  /**
   * Make authenticated request to Atlas API using digest auth
   */
  private async request<T>(endpoint: string, options: { method?: string; data?: any } = {}): Promise<T> {
    try {
      const url = `${this.baseUrl}${endpoint}`;
      const method = options.method || 'GET';

      const fetchOptions: RequestInit = {
        method,
        headers: {
          'Content-Type': 'application/vnd.atlas.2024-08-05+json',
          'Accept': 'application/vnd.atlas.2024-08-05+json'
        }
      };

      if (options.data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
        fetchOptions.body = JSON.stringify(options.data);
      }

      const response = await this.digestFetch.fetch(url, fetchOptions);

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`MongoDB Atlas API error (${response.status}): ${errorText}`);
      }

      // Handle empty responses (like DELETE operations)
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        const text = await response.text();
        return text ? JSON.parse(text) : {} as T;
      }

      return {} as T;
    } catch (error: any) {
      throw new Error(`MongoDB Atlas API request failed: ${error.message}`);
    }
  }

  /**
   * Sanitize project ID for MongoDB database name
   * MongoDB database names can't contain special characters
   */
  private sanitizeProjectId(projectId: string): string {
    return projectId.replace(/[^a-zA-Z0-9]/g, '');
  }

  /**
   * Generate secure password for database user
   */
  private generateSecurePassword(): string {
    return crypto.randomBytes(32).toString('base64url');
  }

  /**
   * Create a database user scoped to a specific database
   */
  async createDatabaseUser(projectId: string): Promise<MongoDBUser> {
    const sanitizedProjectId = this.sanitizeProjectId(projectId);
    const username = `${sanitizedProjectId}_user`;
    const password = this.generateSecurePassword();
    const databaseName = sanitizedProjectId;

    console.log(`🔧 Creating MongoDB user: ${username} for database: ${databaseName}`);

    const atlasUser: AtlasUser = {
      username,
      password,
      databaseName: 'admin', // MongoDB Atlas requires this to be 'admin' for database users
      roles: [
        {
          roleName: 'readWrite',
          databaseName: databaseName, // This is the actual database they can access
        }
      ],
      scopes: [
        {
          name: this.clusterName,
          type: 'CLUSTER'
        }
      ]
    };

    try {
      const result = await this.request<any>(`/groups/${this.groupId}/databaseUsers`, {
        method: 'POST',
        data: atlasUser,
      });

      console.log(`✅ MongoDB user created successfully: ${username}`);

      return {
        username,
        password,
        roles: [
          {
            role: 'readWrite',
            db: databaseName,
          }
        ]
      };
    } catch (error) {
      console.error(`❌ Failed to create MongoDB user: ${username}`, error);
      throw error;
    }
  }

  /**
   * Delete a database user
   */
  async deleteDatabaseUser(projectId: string): Promise<void> {
    const sanitizedProjectId = this.sanitizeProjectId(projectId);
    const username = `${sanitizedProjectId}_user`;

    console.log(`🗑️ Deleting MongoDB user: ${username}`);

    try {
      await this.request(`/groups/${this.groupId}/databaseUsers/admin/${username}`, {
        method: 'DELETE',
      });

      console.log(`✅ MongoDB user deleted successfully: ${username}`);
    } catch (error) {
      console.error(`❌ Failed to delete MongoDB user: ${username}`, error);
      throw error;
    }
  }

  /**
   * Generate connection string for a specific user and database
   */
  generateConnectionString(user: MongoDBUser): string {
    const sanitizedProjectId = user.roles[0].db;
    const clusterUrl = process.env.MONGODB_ATLAS_CLUSTER_URL || 
                      `${this.clusterName}.mongodb.net`;
    
    return `mongodb+srv://${user.username}:${user.password}@${clusterUrl}/${sanitizedProjectId}?retryWrites=true&w=majority`;
  }

  /**
   * Test if Atlas API credentials are working
   */
  async testConnection(): Promise<boolean> {
    try {
      console.log('groupId', this.groupId)
      await this.request(`/groups/${this.groupId}`);
      console.log('✅ MongoDB Atlas API connection successful');
      return true;
    } catch (error) {
      console.error('❌ MongoDB Atlas API connection failed:', error);
      return false;
    }
  }

  /**
   * List existing database users (for debugging)
   */
  async listDatabaseUsers(): Promise<any[]> {
    try {
      const result = await this.request<{ results?: any[] }>(`/groups/${this.groupId}/databaseUsers`);
      return result.results || [];
    } catch (error) {
      console.error('❌ Failed to list database users:', error);
      throw error;
    }
  }
}
