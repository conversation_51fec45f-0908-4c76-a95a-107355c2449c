/**
 * LLM API Service - Instructions for using the built-in LLM API endpoints
 * This service provides documentation for the AI agent on how to integrate LLM capabilities into user apps
 */

export class LLMAPIService {
  /**
   * Get instructions for LLM to understand how to use the LLM API endpoints
   */
  static getLLMInstructions(): string {
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://trymagically.com';
    
    return `
## LLM API Integration
When users need AI capabilities in their apps, use the built-in LLM API endpoints:

**Base URL:** ${baseUrl}

**Available Endpoints:**
* /api/llm/invoke - Pure LLM calls with optional JSON schema for structured output
* /api/llm/chat - Conversation with message history (streaming/non-streaming)
* /api/llm/image - Image generation using DALL-E models

**Usage in React Native:**
\`\`\`typescript
// Pure LLM with structured output
const response = await fetch('${baseUrl}/api/llm/invoke', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    prompt: 'Create a user profile',
    response_json_schema: {
      type: 'object',
      properties: {
        name: { type: 'string' },
        age: { type: 'number' },
        skills: { type: 'array', items: { type: 'string' } }
      }
    }
  })
});

// Chat conversation
const chatResponse = await fetch('${baseUrl}/api/llm/chat', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    messages: [
      { role: 'system', content: 'You are a helpful assistant' },
      { role: 'user', content: 'Hello!' }
    ],
    stream: false
  })
});

// Image generation
const imageResponse = await fetch('${baseUrl}/api/llm/image', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    prompt: 'A beautiful sunset over mountains',
    size: '1024x1024'
  })
});
\`\`\`

**Common Parameters:**
* model - AI model (default: 'gpt-4.1-nano' for text/chat, 'dall-e-3' for images)
* temperature - Creativity 0.0-1.0 (default: 0.7)
* stream - Enable streaming for chat (default: false)

**Invoke Parameters:**
* prompt - Text prompt (required)
* response_json_schema - JSON schema for structured output (optional)

**Chat Parameters:**
* messages - Array with role ('system'|'user'|'assistant') and content (required)

**Image Parameters:**
* prompt - Image description (required)
* model - 'dall-e-2' or 'dall-e-3' (default: 'dall-e-3')
* size - '1024x1024', '1792x1024', '1024x1792' (default: '1024x1024')
* quality - 'standard' or 'hd' (DALL-E 3 only, default: 'standard')
* n - Number of images 1-10 (DALL-E 2 only, DALL-E 3 limited to 1)

**Response Formats:**
\`\`\`typescript
// /api/llm/invoke (plain text)
{ text: "Generated response..." }

// /api/llm/invoke (with schema)
{ name: "John", age: 25, skills: ["React", "TypeScript"] }

// /api/llm/chat
{
  message: { role: "assistant", content: "Response..." },
  usage: { promptTokens: 25, completionTokens: 150, totalTokens: 175 }
}

// /api/llm/image (single)
{
  url: "data:image/png;base64,iVBORw0KGgo...",
  base64: "iVBORw0KGgo...",
  prompt: "Original prompt",
  model: "dall-e-3",
  size: "1024x1024",
  quality: "standard"
}

// /api/llm/image (multiple - DALL-E 2 only)
{
  images: [
    { url: "data:image/png;base64,...", base64: "..." },
    { url: "data:image/png;base64,...", base64: "..." }
  ],
  prompt: "Original prompt",
  model: "dall-e-2",
  size: "1024x1024",
  count: 2
}

// Error responses
{ error: "Descriptive error message" }
\`\`\`

**Error Handling:**
\`\`\`typescript
try {
  const response = await fetch('${baseUrl}/api/llm/invoke', { /* ... */ });
  if (!response.ok) {
    const error = await response.json();
    console.error('LLM API Error:', error.error);
    // Common errors:
    // 400: Missing/invalid parameters, invalid message roles, unsupported models
    // 404: Invalid endpoint
    // 429: Rate limit exceeded
    // 500: Generation failed, API issues
    return;
  }
  const data = await response.json();
  // Use data...
} catch (error) {
  console.error('Network Error:', error);
}
\`\`\`

**Best Practices:**
* Use structured output (JSON schema) for data that needs to be processed
* Use chat endpoint for conversational interfaces with proper message roles
* Use DALL-E 3 for single high-quality images, DALL-E 2 for multiple images
* Handle errors gracefully - API returns descriptive error messages
* Consider streaming for long conversations (set stream: true)
* Validate inputs client-side to avoid 400 errors
* Image generation takes 10-30 seconds - show loading states
* Cache responses when appropriate to reduce API calls
`;
  }

  /**
   * Get concise instructions for quick reference
   */
  static getQuickReference(): string {
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://magically.life';

    return `
LLM API Quick Reference:
• ${baseUrl}/api/llm/invoke - Text/structured output (gpt-4.1-nano default)
• ${baseUrl}/api/llm/chat - Conversations with message history (supports streaming)
• ${baseUrl}/api/llm/image - DALL-E 2/3 image generation (10-30s response time)

Validation: All endpoints validate inputs and return 400 for invalid parameters.
`;
  }

  /**
   * Get example implementations for common use cases
   */
  static getExamples(): string {
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://magically.life';
    
    return `
Common LLM API Use Cases:

1. **Form Validation with AI:**
\`\`\`typescript
const validateForm = async (formData) => {
  const response = await fetch('${baseUrl}/api/llm/invoke', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      prompt: \`Validate this form data: \${JSON.stringify(formData)}\`,
      response_json_schema: {
        type: 'object',
        properties: {
          isValid: { type: 'boolean' },
          errors: { type: 'array', items: { type: 'string' } },
          suggestions: { type: 'array', items: { type: 'string' } }
        }
      }
    })
  });
  return await response.json();
};
\`\`\`

2. **AI Chat Assistant:**
\`\`\`typescript
const [messages, setMessages] = useState([]);

const sendMessage = async (userMessage) => {
  const newMessages = [...messages, { role: 'user', content: userMessage }];
  
  const response = await fetch('${baseUrl}/api/llm/chat', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      messages: newMessages,
      stream: false
    })
  });
  
  const data = await response.json();
  setMessages([...newMessages, data.message]);
};
\`\`\`

3. **Dynamic Image Generation:**
\`\`\`typescript
const generateAvatar = async (description) => {
  const response = await fetch('${baseUrl}/api/llm/image', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      prompt: \`Professional avatar: \${description}, clean background, portrait style\`,
      size: '1024x1024',
      quality: 'hd'
    })
  });
  
  const data = await response.json();
  return data.url; // Base64 data URL ready for Image component
};
\`\`\`
`;
  }
}

export const llmApiService = new LLMAPIService();
