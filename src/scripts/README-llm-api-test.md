# LLM API Comprehensive Test Suite

This script provides comprehensive testing for all LLM API endpoints with extensive permutations and combinations to ensure correctness of inputs and outputs.

## Usage

```bash
# Run the test script
npm run test:llm-api

# Or run directly with tsx
npx tsx src/scripts/test-llm-api.ts
```

## What It Tests

### 1. **Invoke Endpoint (`/api/llm/invoke`)**
- ✅ Plain text generation with various prompts
- ✅ Structured output with JSON schemas (simple and complex)
- ✅ Different models and temperature settings
- ✅ Error cases (missing prompt, invalid parameters)

### 2. **Chat Endpoint (`/api/llm/chat`)**
- ✅ Single message conversations
- ✅ Multi-turn conversations with history
- ✅ System message integration
- ✅ Different models and parameters
- ✅ Error cases (missing messages, invalid format)

### 3. **Image Endpoint (`/api/llm/image`)**
- ✅ Single image generation (DALL-E 2 & 3)
- ✅ Multiple image generation
- ✅ Different sizes and quality settings
- ✅ Error cases (missing prompt, invalid parameters)

### 4. **Edge Cases & Error Scenarios**
- ✅ Invalid endpoints
- ✅ CORS preflight requests
- ✅ Wrong HTTP methods
- ✅ Large prompts
- ✅ Empty requests

## Test Permutations

### **Invoke Endpoint Tests**
1. **Plain Text Variations:**
   - Simple prompts with different temperatures
   - Different model specifications
   - Various prompt lengths and complexities

2. **Structured Output Variations:**
   - Simple schemas (name, age, profession)
   - Complex nested schemas (arrays, objects)
   - Different data types (string, number, boolean, array)

3. **Error Cases:**
   - Missing required fields
   - Invalid parameter values
   - Malformed JSON schemas

### **Chat Endpoint Tests**
1. **Message Variations:**
   - Single user message
   - System + user messages
   - Multi-turn conversations
   - Different message roles

2. **Parameter Variations:**
   - Different models (gpt-4o-mini, gpt-4o)
   - Temperature ranges (0.1 to 0.8)
   - Streaming vs non-streaming

3. **Error Cases:**
   - Missing messages array
   - Empty messages
   - Invalid message format

### **Image Endpoint Tests**
1. **Generation Variations:**
   - Single image (n=1)
   - Multiple images (n=2-4)
   - Different models (dall-e-2, dall-e-3)

2. **Parameter Variations:**
   - Sizes: 1024x1024, 1792x1024, 1024x1792
   - Quality: standard, hd
   - Different prompt styles

3. **Error Cases:**
   - Missing prompt
   - Invalid sizes
   - Unsupported models

## Output Analysis

### **Response Validation**
For each test, the script validates:
- ✅ **HTTP Status Codes**: 200 for success, 400+ for errors
- ✅ **Response Format**: JSON structure matches expected format
- ✅ **Data Types**: All fields have correct data types
- ✅ **Required Fields**: Expected fields are present

### **Performance Metrics**
- ⏱️ **Response Time**: Milliseconds for each request
- 📊 **Success Rate**: Percentage of tests passed
- 🚀 **Performance Analysis**: Fast vs slow responses
- 📈 **Averages**: Mean response times per endpoint

### **Expected Response Formats**

#### Invoke Endpoint
```typescript
// Plain text
{ text: "Generated response..." }

// Structured output
{ name: "John", age: 30, profession: "Engineer" }
```

#### Chat Endpoint
```typescript
{
  message: { 
    role: "assistant", 
    content: "Response content..." 
  },
  usage: { 
    promptTokens: 25, 
    completionTokens: 150, 
    totalTokens: 175 
  }
}
```

#### Image Endpoint
```typescript
// Single image
{
  url: "data:image/png;base64,iVBORw0KGgo...",
  base64: "iVBORw0KGgo...",
  prompt: "Original prompt",
  model: "dall-e-3",
  size: "1024x1024",
  quality: "standard"
}

// Multiple images
{
  images: [
    { url: "data:image/png;base64,...", base64: "..." },
    { url: "data:image/png;base64,...", base64: "..." }
  ],
  count: 2,
  prompt: "Original prompt",
  model: "dall-e-2"
}
```

## Test Results

### **Console Output**
```
🚀 Starting Comprehensive LLM API Tests
==========================================
🌐 Testing against: http://localhost:3000

🧪 Testing /api/llm/invoke endpoint...
  ✅ Plain text - simple prompt - 1,234ms
  ✅ Structured output - complex nested schema - 2,456ms
  ❌ Error case - missing prompt - 45ms

📊 Total Tests: 25
✅ Passed: 23 (92%)
❌ Failed: 2 (8%)
⏱️ Average Response Time: 3,456ms
```

### **Detailed Reports**
- **JSON Reports**: Saved to `results/llm-api-tests/`
- **Summary Data**: Latest results in `latest-summary.json`
- **Full Details**: Timestamped reports with complete request/response data

## Prerequisites

### **Environment Variables**
Ensure these are set in your `.env.local`:
```bash
NEXT_PUBLIC_APP_URL=http://localhost:3000
OPENAI_API_KEY=your_openai_key_here
OPENROUTER_API_KEY=your_openrouter_key_here
```

### **Server Running**
Make sure your development server is running:
```bash
npm run dev
# or
yarn dev
```

## Troubleshooting

### **Common Issues**

1. **Connection Refused**
   - Ensure dev server is running on correct port
   - Check `NEXT_PUBLIC_APP_URL` environment variable

2. **API Key Errors**
   - Verify OpenAI API key is valid
   - Check OpenRouter API key configuration

3. **Image Generation Failures**
   - Image tests require valid OpenAI API key
   - DALL-E 3 may have different rate limits

4. **Timeout Issues**
   - Image generation can take 10-30 seconds
   - Complex structured outputs may be slower

### **Performance Expectations**

- **Text Generation**: 1-5 seconds
- **Structured Output**: 2-8 seconds  
- **Chat Responses**: 1-6 seconds
- **Image Generation**: 10-30 seconds
- **Error Responses**: <1 second

## Comparison with Production

Use this test suite to:
- ✅ Validate API behavior before deployment
- ✅ Ensure response formats match documentation
- ✅ Test error handling and edge cases
- ✅ Monitor performance characteristics
- ✅ Verify CORS and security headers

The comprehensive test coverage ensures your LLM API is production-ready and handles all expected use cases correctly.
